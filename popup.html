<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 350px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      color: #333;
    }
    
    .header p {
      margin: 5px 0 0 0;
      font-size: 12px;
      color: #666;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #333;
    }
    
    input[type="password"], input[type="text"] {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }
    
    input:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    }
    
    .btn {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      width: 100%;
      margin-bottom: 10px;
    }
    
    .btn:hover {
      background: #0056b3;
    }
    
    .btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }
    
    .status {
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
      font-size: 13px;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .instructions {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 4px;
      font-size: 13px;
      line-height: 1.4;
      color: #495057;
    }
    
    .instructions h3 {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: #333;
    }
    
    .instructions ol {
      margin: 10px 0;
      padding-left: 20px;
    }
    
    .instructions li {
      margin-bottom: 5px;
    }
    
    .link {
      color: #007bff;
      text-decoration: none;
    }
    
    .link:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🌐 ChatGPT Translator</h1>
    <p>Dịch văn bản bằng AI</p>
  </div>
  
  <div id="status"></div>
  
  <div class="form-group">
    <label for="apiKey">OpenAI API Key:</label>
    <input type="password" id="apiKey" placeholder="Nhập API key của bạn...">
  </div>
  
  <button id="saveBtn" class="btn">💾 Lưu cấu hình</button>
  <button id="testBtn" class="btn" disabled>🧪 Test API</button>
  
  <div class="instructions">
    <h3>📋 Hướng dẫn sử dụng:</h3>
    <ol>
      <li>Lấy API key từ <a href="https://platform.openai.com/api-keys" target="_blank" class="link">OpenAI</a></li>
      <li>Nhập API key vào ô trên và nhấn "Lưu cấu hình"</li>
      <li>Bôi đen văn bản trên bất kỳ trang web nào</li>
      <li>Nhấn chuột phải và chọn "Dịch với ChatGPT"</li>
      <li>Xem kết quả dịch xuất hiện ở góc phải màn hình</li>
    </ol>
    
    <p><strong>Lưu ý:</strong> API key sẽ được lưu trữ cục bộ và chỉ được sử dụng để gọi API OpenAI.</p>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
