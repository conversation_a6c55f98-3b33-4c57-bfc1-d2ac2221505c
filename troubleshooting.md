# 🔧 Troubleshooting ChatGPT Translator Extension

## 🐛 Vấn đề: Bấm vào icon không thấy g<PERSON> cả

### 📋 <PERSON><PERSON><PERSON> bước debug:

#### 1. Kiểm tra Extension đã load chưa
```
1. Mở chrome://extensions/
2. T<PERSON>m "ChatGPT Translator"
3. <PERSON><PERSON><PERSON> bảo toggle "Enabled" đang bật
4. <PERSON><PERSON><PERSON> có lỗi, nhấn "Reload" extension
```

#### 2. Kiểm tra API Key
```
1. Nhấn icon extension trên toolbar
2. Kiểm tra API key đã nhập chưa
3. Nhấn "Test API" để kiểm tra
4. API key phải bắt đầu bằng "sk-"
```

#### 3. Debug với Console
```
1. Mở trang debug-test.html
2. Bấm F12 mở Developer Tools
3. <PERSON><PERSON><PERSON><PERSON> sang tab Console
4. Bôi đen text và xem log:
   - "Text selection changed: [text]"
   - "Showing translate button for: [text]"
   - "Translating text: [text]"
   - "Received message: {action: 'translateText'}"
```

#### 4. <PERSON><PERSON><PERSON> tra Permissions
```
1. Mở chrome://extensions/
2. Nhấn "Details" trên extension
3. Kiểm tra "Site access" = "On all sites"
4. Nếu không, nhấn "On all sites"
```

#### 5. Kiểm tra Background Script
```
1. Trong chrome://extensions/
2. Nhấn "Details" trên extension
3. Nhấn "Inspect views: service worker"
4. Xem có lỗi trong Console không
```

### 🔍 Các lỗi thường gặp:

#### Lỗi 1: Icon không xuất hiện
**Nguyên nhân:** Content script không load
**Giải pháp:**
- Reload extension
- Refresh trang web
- Kiểm tra permissions

#### Lỗi 2: Icon xuất hiện nhưng không dịch
**Nguyên nhân:** Background script lỗi
**Giải pháp:**
- Kiểm tra API key
- Xem Console background script
- Kiểm tra network requests

#### Lỗi 3: "API Error"
**Nguyên nhân:** API key không hợp lệ
**Giải pháp:**
- Tạo API key mới từ OpenAI
- Kiểm tra credit trong tài khoản
- Test API trong popup

#### Lỗi 4: Extension không load
**Nguyên nhân:** Manifest lỗi
**Giải pháp:**
- Kiểm tra file manifest.json
- Reload extension
- Xem lỗi trong chrome://extensions/

### 🛠️ Debug Commands:

#### Kiểm tra extension trong Console:
```javascript
// Kiểm tra extension API
console.log(chrome.runtime);

// Test gửi message
chrome.runtime.sendMessage({action: "test"});

// Kiểm tra selection
console.log(window.getSelection().toString());
```

#### Reset extension:
```
1. Tắt extension
2. Bật lại extension
3. Refresh tất cả tab
4. Test lại
```

### 📞 Nếu vẫn không hoạt động:

1. **Kiểm tra Chrome version:** Cần Chrome 88+
2. **Kiểm tra Manifest V3:** Extension dùng Manifest V3
3. **Kiểm tra file permissions:** Tất cả file phải readable
4. **Test trên trang khác:** Thử trên trang web khác
5. **Xem error logs:** Kiểm tra tất cả Console logs

### 🔄 Quick Fix:
```
1. Mở chrome://extensions/
2. Tắt extension
3. Bật lại extension  
4. Refresh trang web
5. Test lại
```
