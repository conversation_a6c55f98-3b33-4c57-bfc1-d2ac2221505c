<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Smooth Popup Update</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #007bff;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-section h3 {
            color: #495057;
            margin-top: 0;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .instructions {
            background: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .feature-highlight {
            background: #d4edda;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Test Smooth Popup Update</h1>
        
        <div class="feature-highlight">
            <h3>✨ Tính năng mới: Smooth Update</h3>
            <p><strong>Trước:</strong> Loading popup → Đóng → Mở popup kết quả mới (giật lag)</p>
            <p><strong>Bây giờ:</strong> Loading popup → Update nội dung trực tiếp (mượt mà)</p>
        </div>
        
        <div class="instructions">
            <h3>📋 Hướng dẫn test:</h3>
            <div class="step">
                <strong>Bước 1:</strong> Reload extension trong chrome://extensions/
            </div>
            <div class="step">
                <strong>Bước 2:</strong> Bôi đen text dưới đây
            </div>
            <div class="step">
                <strong>Bước 3:</strong> Nhấn icon 🌐 → Popup loading xuất hiện
            </div>
            <div class="step">
                <strong>Bước 4:</strong> Quan sát popup được update mượt mà (không đóng/mở lại)
            </div>
            <div class="step">
                <strong>Bước 5:</strong> Popup hiển thị kết quả và chỉ đóng khi bạn muốn
            </div>
        </div>

        <div class="test-section">
            <h3>🇻🇳 Test Text Vietnamese:</h3>
            <p>
                Trí tuệ nhân tạo đang cách mạng hóa cách chúng ta làm việc và sống. 
                <span class="highlight">Bôi đen đoạn này để test smooth update!</span>
                Công nghệ này mang lại nhiều cơ hội mới nhưng cũng đặt ra những thách thức về đạo đức.
            </p>
        </div>

        <div class="test-section">
            <h3>🇺🇸 Test Text English:</h3>
            <p>
                Machine learning algorithms can process vast amounts of data and identify patterns. 
                <span class="highlight">Select this text to test the smooth popup update!</span>
                This technology is transforming industries across the globe.
            </p>
        </div>

        <div class="test-section">
            <h3>🔬 Technical Content:</h3>
            <p>
                Chrome extensions use content scripts to interact with web pages. 
                The new update mechanism eliminates the jarring popup recreation by updating the existing DOM element.
                <span class="highlight">Test với đoạn technical này!</span>
            </p>
        </div>

        <div class="test-section">
            <h3>📚 Long Text for Testing:</h3>
            <p>
                Việc phát triển Chrome extension đòi hỏi hiểu biết về Chrome Extension API, 
                cung cấp các phương thức khác nhau để tương tác với chức năng trình duyệt. 
                Extensions có thể sửa đổi trang web thông qua content scripts, 
                xử lý các sự kiện trình duyệt thông qua background scripts, 
                và cung cấp giao diện người dùng thông qua popup pages và options pages.
                <span class="highlight">Đoạn dài này sẽ test khả năng update popup với nội dung nhiều!</span>
            </p>
        </div>

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin-top: 30px;">
            <h3>🎯 Điều cần quan sát:</h3>
            <ul>
                <li>✅ Popup loading xuất hiện ngay lập tức</li>
                <li>✅ Popup được update mượt mà (không nhấp nháy)</li>
                <li>✅ Không có hiện tượng đóng/mở popup</li>
                <li>✅ Vị trí popup không thay đổi</li>
                <li>✅ Animation transition mượt mà</li>
                <li>✅ Event listeners hoạt động bình thường</li>
            </ul>
        </div>

        <footer style="text-align: center; margin-top: 40px; color: #6c757d;">
            <p>🚀 Test smooth popup update - Trải nghiệm mượt mà hơn!</p>
        </footer>
    </div>
</body>
</html>
