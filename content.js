// Content script để hiển thị kết quả dịch trên trang web

let translationPopup = null;
let translateButton = null;
let selectedText = '';
let debounceTimer = null;

// Lắng nghe messages từ background script
chrome.runtime.onMessage.addListener((message) => {
  console.log('ChatGPT Translator: Content script received message:', message);

  switch (message.action) {
    case "showTranslating":
      console.log('ChatGPT Translator: Showing loading popup');
      hideTranslateButton();
      showTranslationPopup(message.text, "Đang dịch...", true);
      break;
    case "showTranslation":
      console.log('ChatGPT Translator: Updating popup with translation result');
      updatePopupWithTranslation(message.originalText, message.translatedText);
      break;
    case "showError":
      console.log('ChatGPT Translator: Updating popup with error');
      updatePopupWithError(message.error);
      break;
  }
});

// Lắng nghe sự kiện chọn text
document.addEventListener('selectionchange', handleTextSelection);

// Ẩn button khi click ra ngoài
document.addEventListener('click', (e) => {
  if (translateButton && !translateButton.contains(e.target)) {
    const selection = window.getSelection();
    if (selection.toString().trim() === '') {
      hideTranslateButton();
    }
  }
});

function handleTextSelection() {
  const selection = window.getSelection();
  const text = selection.toString().trim();
  const rangeCount = selection.rangeCount;
  const isCollapsed = rangeCount > 0 ? selection.getRangeAt(0).collapsed : true;

  // Chỉ hiển thị button khi có text được chọn và selection không collapsed
  if (text && text.length > 0 && rangeCount > 0 && !isCollapsed) {
    selectedText = text;
    // Clear previous debounce timer
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    debounceTimer = setTimeout(() => {
      showTranslateButton(selection);
    }, 500);
  } else {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    hideTranslateButton();
  }
}

function showTranslateButton(selection) {
  hideTranslateButton();

  const range = selection.getRangeAt(0);
  const rect = range.getBoundingClientRect();

  translateButton = document.createElement('div');
  translateButton.id = 'chatgpt-translate-button';

  translateButton.style.cssText = `
    position: fixed;
    top: ${rect.top + window.scrollY - 45}px;
    left: ${rect.left + window.scrollX + (rect.width / 2) - 25}px;
    width: 50px;
    height: 35px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: 2px solid white;
    border-radius: 20px;
    cursor: pointer;
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
    box-shadow: 0 4px 12px rgba(0,123,255,0.4);
    transition: all 0.2s ease;
    user-select: none;
    pointer-events: auto;
    animation: bounceIn 0.3s ease-out;
  `;

  translateButton.innerHTML = '🌐';
  translateButton.title = 'Dịch với ChatGPT - Click để dịch';

  // Thêm attribute để dễ debug
  translateButton.setAttribute('data-translator', 'button');

  // Thêm animation CSS nếu chưa có
  if (!document.getElementById('translate-button-styles')) {
    const style = document.createElement('style');
    style.id = 'translate-button-styles';
    style.textContent = `
      @keyframes bounceIn {
        0% {
          transform: scale(0.3);
          opacity: 0;
        }
        50% {
          transform: scale(1.1);
        }
        100% {
          transform: scale(1);
          opacity: 1;
        }
      }

      #chatgpt-translate-button:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 16px rgba(0,123,255,0.6);
      }

      #chatgpt-translate-button:active {
        transform: scale(0.95);
      }
    `;
    document.head.appendChild(style);
  }

  // Xử lý click
  translateButton.addEventListener('click', (e) => {
    console.log('ChatGPT Translator: Button clicked!');
    e.preventDefault();
    e.stopPropagation();
    translateSelectedText();
  });

  // Thêm event listener cho mousedown để đảm bảo click được detect
  translateButton.addEventListener('mousedown', (e) => {
    console.log('ChatGPT Translator: Button mousedown!');
    e.preventDefault();
    e.stopPropagation();
  });

  // Backup event listeners
  translateButton.addEventListener('touchstart', (e) => {
    console.log('ChatGPT Translator: Button touchstart!');
    e.preventDefault();
    e.stopPropagation();
    translateSelectedText();
  });

  translateButton.addEventListener('mouseup', (e) => {
    console.log('ChatGPT Translator: Button mouseup!');
    e.preventDefault();
    e.stopPropagation();
    translateSelectedText();
  });

  document.body.appendChild(translateButton);
}

function hideTranslateButton() {
  if (translateButton) {
    translateButton.remove();
    translateButton = null;
  }
}

function translateSelectedText() {
  if (!selectedText) {
    console.log('ChatGPT Translator: No text selected');
    return;
  }

  console.log('ChatGPT Translator: Translating text:', selectedText);

  // Hiển thị loading ngay lập tức
  hideTranslateButton();
  showTranslationPopup(selectedText, "Đang dịch...", true);

  // Gửi message đến background script để dịch
  chrome.runtime.sendMessage({
    action: "translateText",
    text: selectedText
  }, (response) => {
    if (chrome.runtime.lastError) {
      console.error('ChatGPT Translator: Error sending message:', chrome.runtime.lastError);
      showTranslationPopup("", `Lỗi kết nối: ${chrome.runtime.lastError.message}`, false, true);
    } else {
      console.log('ChatGPT Translator: Message sent successfully:', response);
    }
  });
}

function showTranslationPopup(originalText, translatedText, isLoading = false, isError = false) {
  // Xóa popup cũ nếu có
  if (translationPopup) {
    translationPopup.remove();
  }

  // Tạo popup mới
  translationPopup = document.createElement('div');
  translationPopup.id = 'chatgpt-translator-popup';

  // CSS styles
  translationPopup.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    padding: 0;
    animation: slideIn 0.3s ease-out;
  `;

  // Thêm animation CSS
  if (!document.getElementById('chatgpt-translator-styles')) {
    const style = document.createElement('style');
    style.id = 'chatgpt-translator-styles';
    style.textContent = `
      @keyframes slideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
    `;
    document.head.appendChild(style);
  }

  let content = '';

  if (isError) {
    content = `
      <div style="padding: 16px; border-bottom: 1px solid #eee; background: #f8f9fa; border-radius: 8px 8px 0 0;">
        <div style="font-weight: 600; color: #dc3545; margin-bottom: 8px;">❌ Lỗi dịch thuật</div>
      </div>
      <div style="padding: 16px;">
        <div style="color: #dc3545;">${translatedText}</div>
      </div>
    `;
  } else if (isLoading) {
    content = `
      <div style="padding: 16px; border-bottom: 1px solid #eee; background: #f8f9fa; border-radius: 8px 8px 0 0;">
        <div style="font-weight: 600; color: #007bff; margin-bottom: 8px;">🔄 ChatGPT Translator</div>
      </div>
      <div style="padding: 16px;">
        <div style="color: #666; margin-bottom: 12px;"><strong>Văn bản gốc:</strong></div>
        <div style="background: #f8f9fa; padding: 8px; border-radius: 4px; margin-bottom: 12px; border-left: 3px solid #007bff;">${originalText}</div>
        <div style="color: #007bff; display: flex; align-items: center;">
          <span style="margin-right: 8px;">⏳</span>
          ${translatedText}
        </div>
      </div>
    `;
  } else {
    content = `
      <div style="padding: 16px; border-bottom: 1px solid #eee; background: #f8f9fa; border-radius: 8px 8px 0 0;">
        <div style="font-weight: 600; color: #28a745; margin-bottom: 8px;">✅ ChatGPT Translator</div>
      </div>
      <div style="padding: 16px;">
        <div style="color: #666; margin-bottom: 8px;"><strong>Văn bản gốc:</strong></div>
        <div style="background: #f8f9fa; padding: 8px; border-radius: 4px; margin-bottom: 12px; border-left: 3px solid #6c757d;">${originalText}</div>
        <div style="color: #666; margin-bottom: 8px;"><strong>Bản dịch:</strong></div>
        <div style="background: #e8f5e8; padding: 8px; border-radius: 4px; border-left: 3px solid #28a745;">${translatedText}</div>
        <div style="margin-top: 12px; text-align: right;">
          <button id="copy-translation" style="background: #007bff; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">📋 Sao chép</button>
        </div>
      </div>
    `;
  }

  // Thêm nút đóng
  content += `
    <button id="close-translation" style="position: absolute; top: 8px; right: 8px; background: none; border: none; font-size: 18px; cursor: pointer; color: #999; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">×</button>
  `;

  translationPopup.innerHTML = content;
  document.body.appendChild(translationPopup);

  // Attach event listeners
  if (!isLoading) {
    attachPopupEventListeners(translatedText);
  } else {
    // Cho loading popup, chỉ cần close button
    const closeBtn = translationPopup.querySelector('#close-translation');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        if (translationPopup) {
          translationPopup.remove();
          translationPopup = null;
        }
      });
    }
  }
}

// Function để update popup với kết quả dịch (không tạo popup mới)
function updatePopupWithTranslation(originalText, translatedText) {
  if (!translationPopup) {
    // Nếu không có popup, tạo mới
    showTranslationPopup(originalText, translatedText, false);
    return;
  }

  console.log('ChatGPT Translator: Updating existing popup with translation');

  // Update nội dung popup hiện tại
  const content = `
    <div style="padding: 16px; border-bottom: 1px solid #eee; background: #f8f9fa; border-radius: 8px 8px 0 0;">
      <div style="font-weight: 600; color: #28a745; margin-bottom: 8px;">✅ ChatGPT Translator</div>
    </div>
    <div style="padding: 16px;">
      <div style="color: #666; margin-bottom: 8px;"><strong>Văn bản gốc:</strong></div>
      <div style="background: #f8f9fa; padding: 8px; border-radius: 4px; margin-bottom: 12px; border-left: 3px solid #6c757d;">${originalText}</div>
      <div style="color: #666; margin-bottom: 8px;"><strong>Bản dịch:</strong></div>
      <div style="background: #e8f5e8; padding: 8px; border-radius: 4px; border-left: 3px solid #28a745;">${translatedText}</div>
      <div style="margin-top: 12px; text-align: right;">
        <button id="copy-translation" style="background: #007bff; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">📋 Sao chép</button>
      </div>
    </div>
    <button id="close-translation" style="position: absolute; top: 8px; right: 8px; background: none; border: none; font-size: 18px; cursor: pointer; color: #999; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">×</button>
  `;

  // Update innerHTML với animation
  translationPopup.style.transition = 'all 0.3s ease';
  translationPopup.innerHTML = content;

  // Re-attach event listeners
  attachPopupEventListeners(translatedText);
}

// Function để update popup với lỗi
function updatePopupWithError(errorMessage) {
  if (!translationPopup) {
    // Nếu không có popup, tạo mới
    showTranslationPopup("", `Lỗi: ${errorMessage}`, false, true);
    return;
  }

  console.log('ChatGPT Translator: Updating existing popup with error');

  const content = `
    <div style="padding: 16px; border-bottom: 1px solid #eee; background: #f8f9fa; border-radius: 8px 8px 0 0;">
      <div style="font-weight: 600; color: #dc3545; margin-bottom: 8px;">❌ Lỗi dịch thuật</div>
    </div>
    <div style="padding: 16px;">
      <div style="color: #dc3545;">Lỗi: ${errorMessage}</div>
    </div>
    <button id="close-translation" style="position: absolute; top: 8px; right: 8px; background: none; border: none; font-size: 18px; cursor: pointer; color: #999; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">×</button>
  `;

  translationPopup.innerHTML = content;

  // Re-attach event listeners
  attachPopupEventListeners();
}

// Function để attach event listeners cho popup
function attachPopupEventListeners(translatedText = null) {
  // Function để đóng popup
  function closePopup() {
    if (translationPopup) {
      translationPopup.remove();
      translationPopup = null;
      document.removeEventListener('click', handleClickOutside);
    }
  }

  // Function để xử lý click ra ngoài
  function handleClickOutside(event) {
    if (translationPopup && !translationPopup.contains(event.target)) {
      closePopup();
    }
  }

  // Close button
  const closeBtn = translationPopup.querySelector('#close-translation');
  if (closeBtn) {
    closeBtn.addEventListener('click', closePopup);
  }

  // Copy button
  const copyBtn = translationPopup.querySelector('#copy-translation');
  if (copyBtn && translatedText) {
    copyBtn.addEventListener('click', () => {
      navigator.clipboard.writeText(translatedText).then(() => {
        copyBtn.textContent = '✅ Đã sao chép';
        setTimeout(() => {
          copyBtn.textContent = '📋 Sao chép';
        }, 2000);
      });
    });
  }

  // Click outside listener (với delay để tránh đóng ngay)
  setTimeout(() => {
    document.addEventListener('click', handleClickOutside);
  }, 100);
}
