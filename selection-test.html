<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Text Selection Detection</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #007bff;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-section h3 {
            color: #495057;
            margin-top: 0;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .instructions {
            background: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }
        
        .test-case {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Text Selection Detection</h1>
        
        <div class="instructions">
            <h3>🐛 Bug đã fix:</h3>
            <p><strong>Vấn đề:</strong> Double click text → Click ra ngoài để deselect → Icon không biến mất</p>
            <p><strong>Nguyên nhân:</strong> <code>window.getSelection()</code> vẫn có object nhưng <code>collapsed = true</code></p>
            <p><strong>Giải pháp:</strong> Kiểm tra thêm <code>rangeCount</code> và <code>isCollapsed</code></p>
        </div>

        <div class="instructions">
            <h3>📋 Test cases:</h3>
            <div class="step">
                <strong>Test 1:</strong> Double click text → Icon xuất hiện
            </div>
            <div class="step">
                <strong>Test 2:</strong> Click ra ngoài → Icon biến mất
            </div>
            <div class="step">
                <strong>Test 3:</strong> Drag select text → Icon xuất hiện
            </div>
            <div class="step">
                <strong>Test 4:</strong> Click vào text khác → Icon di chuyển
            </div>
            <div class="step">
                <strong>Test 5:</strong> Keyboard select (Shift + Arrow) → Icon xuất hiện
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Test Case 1: Double Click</h3>
            <div class="test-case">
                <p>Double click vào từ <span class="highlight">này</span> để select, sau đó click ra ngoài để deselect.</p>
                <p>Icon phải biến mất khi click ra ngoài.</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Test Case 2: Drag Select</h3>
            <div class="test-case">
                <p>Drag để select <span class="highlight">đoạn text này có nhiều từ</span> và xem icon xuất hiện.</p>
                <p>Sau đó click ra ngoài để test deselect.</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Test Case 3: Multiple Paragraphs</h3>
            <div class="test-case">
                <p>Paragraph đầu tiên có text để test selection. Double click vào bất kỳ từ nào.</p>
                <p>Paragraph thứ hai cũng có text. Click vào đây sau khi đã select text ở trên.</p>
                <p>Paragraph thứ ba để test việc di chuyển selection giữa các đoạn khác nhau.</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Test Case 4: Mixed Content</h3>
            <div class="test-case">
                <p>Text bình thường, <strong>text bold</strong>, <em>text italic</em>, và <code>code text</code>.</p>
                <p>Test selection trên các loại formatting khác nhau.</p>
            </div>
        </div>

        <div class="debug-info">
            <h4>🔍 Debug Console Output:</h4>
            <p>Mở Console (F12) để xem debug info:</p>
            <ul>
                <li><code>text</code>: Nội dung được select</li>
                <li><code>rangeCount</code>: Số lượng range (thường là 1 khi có selection)</li>
                <li><code>isCollapsed</code>: true nếu selection rỗng (cursor), false nếu có text</li>
            </ul>
            
            <h4>✅ Expected Behavior:</h4>
            <ul>
                <li>Icon chỉ hiện khi: <code>text.length > 0 && rangeCount > 0 && !isCollapsed</code></li>
                <li>Icon ẩn khi: text rỗng HOẶC rangeCount = 0 HOẶC isCollapsed = true</li>
            </ul>
        </div>

        <footer style="text-align: center; margin-top: 40px; color: #6c757d;">
            <p>🔧 Test selection detection - Icon chỉ hiện khi thực sự có text được chọn!</p>
        </footer>
    </div>

    <script>
        // Debug script để log selection events
        console.log('🔍 Selection test page loaded');
        
        document.addEventListener('selectionchange', () => {
            const selection = window.getSelection();
            const text = selection.toString().trim();
            const rangeCount = selection.rangeCount;
            const isCollapsed = rangeCount > 0 ? selection.getRangeAt(0).collapsed : true;
            
            console.log('🔍 Native selectionchange:', {
                text: text,
                textLength: text.length,
                rangeCount: rangeCount,
                isCollapsed: isCollapsed,
                shouldShowIcon: text.length > 0 && rangeCount > 0 && !isCollapsed
            });
        });
        
        document.addEventListener('mouseup', () => {
            console.log('🔍 Native mouseup event');
        });
        
        document.addEventListener('click', (e) => {
            console.log('🔍 Native click on:', e.target.tagName, e.target.textContent?.substring(0, 50));
        });
    </script>
</body>
</html>
