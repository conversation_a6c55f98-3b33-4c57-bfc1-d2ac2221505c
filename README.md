# ChatGPT Translator - Chrome Extension

Một Chrome extension đơn giản để dịch văn bản đượ<PERSON> chọn bằng ChatGPT API.

## ✨ Tính năng

- 🖱️ Bôi đen văn bản và nhấn chuột phải để dịch
- 🤖 Sử dụng ChatGPT API để dịch chính xác
- 🌐 Tự động phát hiện ngôn ngữ (Việt ↔ Anh)
- 📋 Sao chép kết quả dịch dễ dàng
- 💾 Lưu trữ API key an toàn cục bộ

## 🚀 Cài đặt

### 1. Chuẩn bị API Key

1. Truy cập [OpenAI Platform](https://platform.openai.com/api-keys)
2. Đăng nhập và tạo API key mới
3. Sao chép API key (bắt đầu bằng `sk-`)

### 2. Cài đặt Extension

1. Tải source code về máy
2. Mở Chrome và truy cập `chrome://extensions/`
3. <PERSON><PERSON><PERSON> "Developer mode" ở góc trên bên phải
4. <PERSON><PERSON><PERSON><PERSON> "Load unpacked" và chọn thư mục chứa extension
5. Extension sẽ xuất hiện trong danh sách

### 3. Cấu hình

1. Nhấn vào icon extension trên thanh công cụ
2. Nhập API key vào ô "OpenAI API Key"
3. Nhấn "Lưu cấu hình"
4. Có thể nhấn "Test API" để kiểm tra

## 📖 Cách sử dụng

1. Truy cập bất kỳ trang web nào
2. Bôi đen đoạn văn bản muốn dịch
3. Nhấn chuột phải và chọn "Dịch với ChatGPT"
4. Đợi kết quả hiển thị ở góc phải màn hình
5. Có thể sao chép kết quả bằng nút "Sao chép"

## 🔧 Cấu trúc file

```
translator-ai/
├── manifest.json          # Cấu hình extension
├── background.js          # Service worker xử lý API
├── content.js            # Script hiển thị kết quả
├── popup.html            # Giao diện popup cấu hình
├── popup.js              # Logic popup
├── icons/                # Thư mục chứa icon
└── README.md             # Hướng dẫn này
```

## 🛠️ Tùy chỉnh

### Thay đổi prompt dịch thuật

Chỉnh sửa trong file `background.js`, phần `system` message:

```javascript
{
  role: 'system',
  content: 'Prompt tùy chỉnh của bạn...'
}
```

### Thay đổi model AI

Trong file `background.js`, thay đổi:

```javascript
model: 'gpt-4', // Thay vì 'gpt-3.5-turbo'
```

## 🔒 Bảo mật

- API key được lưu trữ cục bộ trong Chrome storage
- Chỉ được sử dụng để gọi API OpenAI
- Không được gửi đến server nào khác

## 🐛 Xử lý lỗi

### "Vui lòng cấu hình API Key"
- Kiểm tra đã nhập API key chưa
- Đảm bảo API key bắt đầu bằng `sk-`

### "API Error"
- Kiểm tra API key còn hạn sử dụng
- Đảm bảo tài khoản OpenAI có credit

### Extension không hoạt động
- Reload extension trong `chrome://extensions/`
- Kiểm tra console để xem lỗi chi tiết

## 📝 License

MIT License - Tự do sử dụng và chỉnh sửa.

## 🤝 Đóng góp

Mọi đóng góp và phản hồi đều được chào đón!
