# ChatGPT Translator - Chrome Extension

Một Chrome extension đơn giản để dịch văn bản được chọn bằng ChatGPT API.

## ✨ Tính năng

- 🎯 **Dịch nhanh**: Bôi đen văn bản → Nhấn icon 🌐 xuất hiện
- 🖱️ **Chuột phải**: Vẫn có thể dùng menu "Dịch với ChatGPT"
- 🤖 Sử dụng ChatGPT API để dịch chính xác
- 🌐 Tự động phát hiện ngôn ngữ (Việt ↔ Anh)
- 📋 Sao chép kết quả dịch dễ dàng
- 💾 Lưu trữ API key an toàn cục bộ
- ✨ Giao diện đẹp với animation mượt mà

## 🚀 Cài đặt

### 1. Chuẩn bị API Key

1. Truy cập [OpenAI Platform](https://platform.openai.com/api-keys)
2. Đ<PERSON>ng nhập và tạo API key mới
3. Sao chép API key (bắt đầu bằng `sk-`)

### 2. Cài đặt Extension

1. Tải source code về máy
2. Mở Chrome và truy cập `chrome://extensions/`
3. Bật "Developer mode" ở góc trên bên phải
4. Nhấn "Load unpacked" và chọn thư mục chứa extension
5. Extension sẽ xuất hiện trong danh sách

### 3. Cấu hình

1. Nhấn vào icon extension trên thanh công cụ
2. Nhập API key vào ô "OpenAI API Key"
3. Nhấn "Lưu cấu hình"
4. Có thể nhấn "Test API" để kiểm tra

## 📖 Cách sử dụng

### 🎯 Cách 1: Dịch nhanh (Khuyến nghị)
1. Truy cập bất kỳ trang web nào
2. Bôi đen đoạn văn bản muốn dịch
3. **Nhấn vào icon 🌐 xuất hiện** phía trên văn bản
4. Xem kết quả hiển thị ở góc phải màn hình

### 🖱️ Cách 2: Menu chuột phải
1. Bôi đen văn bản muốn dịch
2. Nhấn chuột phải → Chọn "Dịch với ChatGPT"
3. Xem kết quả hiển thị

### 📋 Thao tác với kết quả
- Nhấn "📋 Sao chép" để copy bản dịch
- Nhấn "×" để đóng popup
- Click ra ngoài popup để đóng
- Popup sẽ luôn hiển thị cho đến khi bạn đóng

## 🔧 Cấu trúc file

```
translator-ai/
├── manifest.json          # Cấu hình extension
├── background.js          # Service worker xử lý API
├── content.js            # Script hiển thị kết quả
├── popup.html            # Giao diện popup cấu hình
├── popup.js              # Logic popup
├── icons/                # Thư mục chứa icon
└── README.md             # Hướng dẫn này
```

## 🛠️ Tùy chỉnh

### Thay đổi prompt dịch thuật

Chỉnh sửa trong file `background.js`, phần `system` message:

```javascript
{
  role: 'system',
  content: 'Prompt tùy chỉnh của bạn...'
}
```

### Thay đổi model AI

Trong file `background.js`, thay đổi:

```javascript
model: 'gpt-4', // Thay vì 'gpt-3.5-turbo'
```

## 🔒 Bảo mật

- API key được lưu trữ cục bộ trong Chrome storage
- Chỉ được sử dụng để gọi API OpenAI
- Không được gửi đến server nào khác

## 🐛 Xử lý lỗi

### "Vui lòng cấu hình API Key"
- Kiểm tra đã nhập API key chưa
- Đảm bảo API key bắt đầu bằng `sk-`

### "API Error"
- Kiểm tra API key còn hạn sử dụng
- Đảm bảo tài khoản OpenAI có credit

### Extension không hoạt động
- Reload extension trong `chrome://extensions/`
- Kiểm tra console để xem lỗi chi tiết

## 📝 License

MIT License - Tự do sử dụng và chỉnh sửa.

## 🤝 Đóng góp

Mọi đóng góp và phản hồi đều được chào đón!
