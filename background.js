// Background script để xử lý context menu và API calls

// Tạo context menu khi extension được cài đặt
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: "translateText",
    title: "Dịch với ChatGPT",
    contexts: ["selection"]
  });
});

// Xử lý khi người dùng click vào context menu
chrome.contextMenus.onClicked.addListener(async (info, tab) => {
  if (info.menuItemId === "translateText" && info.selectionText) {
    const selectedText = info.selectionText.trim();
    await handleTranslation(selectedText, tab.id);
  }
});

// X<PERSON> lý messages từ content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('ChatGPT Translator: Received message:', message);

  if (message.action === "translateText" && message.text) {
    // Xử lý async và trả response
    handleTranslation(message.text, sender.tab.id)
      .then(() => {
        sendResponse({ success: true });
      })
      .catch((error) => {
        console.error('ChatGPT Translator: Error in handleTranslation:', error);
        sendResponse({ success: false, error: error.message });
      });

    return true; // Giữ message port mở cho async response
  }

  // Trả response ngay cho các message khác
  sendResponse({ success: false, error: "Unknown action" });
});

// Hàm xử lý dịch thuật chung
async function handleTranslation(selectedText, tabId) {
  console.log('ChatGPT Translator: Starting translation for:', selectedText);

  if (!selectedText) {
    console.log('ChatGPT Translator: No text provided');
    throw new Error('No text provided');
  }

  try {
    // Gọi API ChatGPT để dịch
    console.log('ChatGPT Translator: Calling ChatGPT API...');
    const translation = await translateWithChatGPT(selectedText);
    console.log('ChatGPT Translator: Translation received:', translation);

    // Gửi kết quả về content script
    chrome.tabs.sendMessage(tabId, {
      action: "showTranslation",
      originalText: selectedText,
      translatedText: translation
    });

    return translation;
  } catch (error) {
    console.error("ChatGPT Translator: Translation error:", error);
    chrome.tabs.sendMessage(tabId, {
      action: "showError",
      error: error.message
    });
    throw error;
  }
}

// Hàm gọi ChatGPT API
async function translateWithChatGPT(text) {
  // Lấy API key từ storage
  const result = await chrome.storage.sync.get(['openaiApiKey']);
  const apiKey = result.openaiApiKey;

  if (!apiKey) {
    throw new Error("Vui lòng cấu hình API Key trong popup extension");
  }

  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: 'Bạn là một trợ lý dịch thuật chuyên nghiệp. Hãy dịch văn bản được cung cấp sang tiếng Việt một cách tự nhiên và chính xác. Nếu văn bản đã là tiếng Việt, hãy dịch sang tiếng Anh. Chỉ trả về bản dịch, không cần giải thích thêm.'
        },
        {
          role: 'user',
          content: text
        }
      ],
      max_tokens: 1000,
      temperature: 0.3
    })
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`API Error: ${errorData.error?.message || 'Unknown error'}`);
  }

  const data = await response.json();
  return data.choices[0].message.content.trim();
}
