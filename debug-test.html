<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug ChatGPT Translator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-text {
            background: #f0f8ff;
            padding: 20px;
            border: 2px dashed #007bff;
            border-radius: 8px;
            margin: 20px 0;
            font-size: 16px;
        }
        
        .instructions {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>🐛 Debug ChatGPT Translator Extension</h1>
    
    <div class="instructions">
        <h3>📋 Hướng dẫn debug:</h3>
        <ol>
            <li>Mở Developer Tools (F12)</li>
            <li><PERSON><PERSON><PERSON><PERSON> sang tab Console</li>
            <li><PERSON>ôi đen text dưới đây</li>
            <li>Nhấn icon 🌐 xuất hiện</li>
            <li>Xem log trong Console</li>
        </ol>
    </div>

    <div class="test-text">
        <h3>🇻🇳 Test Text Vietnamese:</h3>
        <p>Xin chào, đây là văn bản tiếng Việt để test extension dịch thuật.</p>
    </div>

    <div class="test-text">
        <h3>🇺🇸 Test Text English:</h3>
        <p>Hello, this is English text to test the translation extension.</p>
    </div>

    <div class="debug-info">
        <h4>🔍 Debug Checklist:</h4>
        <ul>
            <li>✅ Extension loaded in chrome://extensions/</li>
            <li>✅ API key configured in popup</li>
            <li>✅ Console shows "Text selection changed" when selecting text</li>
            <li>✅ Icon 🌐 appears when text is selected</li>
            <li>✅ Console shows "Translating text" when clicking icon</li>
            <li>✅ Console shows "Received message" in background</li>
            <li>✅ Translation popup appears</li>
        </ul>
    </div>

    <script>
        // Debug script để log events
        console.log('🐛 Debug page loaded');
        
        document.addEventListener('mouseup', () => {
            const selection = window.getSelection().toString().trim();
            if (selection) {
                console.log('🐛 Text selected on page:', selection);
            }
        });
        
        // Test extension presence
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            console.log('🐛 Chrome extension API available');
        } else {
            console.log('🐛 Chrome extension API not available');
        }
    </script>
</body>
</html>
