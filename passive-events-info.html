<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Passive Event Listeners Info</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #dc3545;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .warning {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #dc3545;
        }
        
        .solution {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #17a2b8;
        }
        
        .code {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            border: 1px solid #dee2e6;
            margin: 10px 0;
        }
        
        .good {
            border-left: 4px solid #28a745;
        }
        
        .bad {
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚠️ Passive Event Listeners Warning</h1>
        
        <div class="warning">
            <h3>🚨 Lỗi gặp phải:</h3>
            <p><strong>[Violation] Added non-passive event listener to a scroll-blocking 'touchstart' event.</strong></p>
            <p>Chrome cảnh báo vì touchstart event có thể block scroll performance trên mobile.</p>
        </div>

        <div class="info">
            <h3>📚 Passive Event Listeners là gì?</h3>
            <p><strong>Passive Event Listener</strong> là event listener được đánh dấu không bao giờ gọi <code>preventDefault()</code>.</p>
            <p>Điều này cho phép browser tối ưu performance, đặc biệt là scroll trên mobile.</p>
        </div>

        <div class="info">
            <h3>🔍 Tại sao có warning?</h3>
            <ul>
                <li><strong>touchstart/touchmove</strong> events có thể block scroll</li>
                <li>Browser phải chờ event handler hoàn thành trước khi scroll</li>
                <li>Nếu handler chậm → scroll bị lag</li>
                <li>Chrome khuyến khích dùng passive để cải thiện performance</li>
            </ul>
        </div>

        <div class="code bad">
            <h4>❌ Code gây warning:</h4>
            <pre>translateButton.addEventListener('touchstart', (e) => {
  e.preventDefault(); // ← Gọi preventDefault()
  e.stopPropagation();
  translateSelectedText();
});</pre>
        </div>

        <div class="solution">
            <h3>✅ Giải pháp đã áp dụng:</h3>
            <p><strong>Bỏ touchstart event listener</strong> vì không cần thiết:</p>
            <ul>
                <li>Click event đã đủ cho cả desktop và mobile</li>
                <li>Modern browsers tự động handle touch → click</li>
                <li>Giảm complexity và tránh performance issues</li>
            </ul>
        </div>

        <div class="code good">
            <h4>✅ Code sau khi fix:</h4>
            <pre>// Chỉ cần click và mouseup events
translateButton.addEventListener('click', (e) => {
  e.preventDefault();
  e.stopPropagation();
  translateSelectedText();
});

translateButton.addEventListener('mouseup', (e) => {
  e.preventDefault();
  e.stopPropagation();
  translateSelectedText();
});</pre>
        </div>

        <div class="info">
            <h3>🎯 Khi nào cần Passive Events?</h3>
            <div class="code">
                <h4>Nếu KHÔNG gọi preventDefault():</h4>
                <pre>element.addEventListener('touchstart', handler, { passive: true });</pre>
            </div>
            <div class="code">
                <h4>Nếu CÓ gọi preventDefault():</h4>
                <pre>// Không thể dùng passive: true
element.addEventListener('touchstart', handler); // Default: passive: false</pre>
            </div>
        </div>

        <div class="info">
            <h3>📱 Mobile Touch Events:</h3>
            <ul>
                <li><strong>touchstart:</strong> Khi finger chạm screen</li>
                <li><strong>touchmove:</strong> Khi finger di chuyển</li>
                <li><strong>touchend:</strong> Khi finger rời screen</li>
                <li><strong>click:</strong> Được fire sau touchend (300ms delay trên cũ)</li>
            </ul>
        </div>

        <div class="solution">
            <h3>🚀 Best Practices:</h3>
            <ol>
                <li><strong>Ưu tiên click event</strong> cho button interactions</li>
                <li><strong>Dùng passive: true</strong> cho scroll listeners</li>
                <li><strong>Tránh preventDefault()</strong> trong touch events nếu có thể</li>
                <li><strong>Test trên mobile</strong> để đảm bảo performance</li>
            </ol>
        </div>

        <div class="code good">
            <h4>✅ Ví dụ passive event cho scroll:</h4>
            <pre>// Cho scroll listeners - không cần preventDefault()
window.addEventListener('scroll', handleScroll, { passive: true });
window.addEventListener('touchmove', handleTouchMove, { passive: true });</pre>
        </div>

        <div class="info">
            <h3>🔗 Tham khảo:</h3>
            <ul>
                <li><a href="https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#passive" target="_blank">MDN: Passive Event Listeners</a></li>
                <li><a href="https://www.chromestatus.com/feature/5745543795965952" target="_blank">Chrome Status: Passive Event Listeners</a></li>
                <li><a href="https://developers.google.com/web/updates/2016/06/passive-event-listeners" target="_blank">Google Developers: Passive Event Listeners</a></li>
            </ul>
        </div>

        <footer style="text-align: center; margin-top: 40px; color: #6c757d;">
            <p>✅ Warning đã được fix bằng cách bỏ touchstart event!</p>
        </footer>
    </div>
</body>
</html>
