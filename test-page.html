<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test ChatGPT Translator Extension</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #007bff;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-section h3 {
            color: #495057;
            margin-top: 0;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .instructions {
            background: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 Test ChatGPT Translator Extension</h1>
        
        <div class="instructions">
            <h3>📋 Hướng dẫn test:</h3>
            <div class="step">
                <strong>Bước 1:</strong> Cài đặt extension và cấu hình API key
            </div>
            <div class="step">
                <strong>Bước 2:</strong> Bôi đen bất kỳ đoạn text nào dưới đây
            </div>
            <div class="step">
                <strong>Bước 3:</strong> Nhấn vào icon 🌐 xuất hiện hoặc chuột phải chọn "Dịch với ChatGPT"
            </div>
            <div class="step">
                <strong>Bước 4:</strong> Xem kết quả dịch ở góc phải màn hình
            </div>
        </div>

        <div class="test-section">
            <h3>🇻🇳 Văn bản tiếng Việt (sẽ dịch sang tiếng Anh)</h3>
            <p>
                Trí tuệ nhân tạo đang thay đổi cách chúng ta làm việc và sống. 
                Công nghệ này mang lại nhiều cơ hội mới nhưng cũng đặt ra những thách thức về đạo đức và xã hội.
            </p>
            <p>
                Việt Nam đang nỗ lực phát triển công nghệ thông tin và trở thành một trung tâm công nghệ trong khu vực.
                <span class="highlight">Hãy bôi đen đoạn này để test extension!</span>
            </p>
        </div>

        <div class="test-section">
            <h3>🇺🇸 English Text (will translate to Vietnamese)</h3>
            <p>
                Artificial intelligence is revolutionizing the way we work and live. 
                This technology brings many new opportunities but also raises ethical and social challenges.
            </p>
            <p>
                Machine learning algorithms can process vast amounts of data and identify patterns that humans might miss.
                <span class="highlight">Try selecting this text to test the extension!</span>
            </p>
        </div>

        <div class="test-section">
            <h3>🔬 Technical Content</h3>
            <p>
                React is a JavaScript library for building user interfaces. 
                It uses a virtual DOM to efficiently update the UI when data changes.
            </p>
            <p>
                <code>const [count, setCount] = useState(0);</code> - This is a React Hook that manages state in functional components.
            </p>
        </div>

        <div class="test-section">
            <h3>📚 Mixed Content</h3>
            <p>
                Chrome extensions sử dụng manifest.json để define permissions và configuration. 
                Content scripts có thể interact với DOM của web pages.
            </p>
            <p>
                Background scripts chạy trong background và handle events như context menu clicks.
            </p>
        </div>

        <div class="test-section">
            <h3>🎯 Short Phrases</h3>
            <ul>
                <li>Hello world</li>
                <li>Xin chào thế giới</li>
                <li>Good morning</li>
                <li>Chúc ngủ ngon</li>
                <li>Thank you very much</li>
                <li>Cảm ơn bạn rất nhiều</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📖 Long Paragraph</h3>
            <p>
                The development of Chrome extensions involves understanding the Chrome Extension API, 
                which provides various methods for interacting with browser functionality. 
                Extensions can modify web pages through content scripts, handle browser events through background scripts, 
                and provide user interfaces through popup pages and options pages. 
                The manifest file serves as the configuration center, defining permissions, scripts, and other extension metadata. 
                Modern extensions use Manifest V3, which introduces service workers instead of background pages and 
                requires more explicit permission declarations for enhanced security.
            </p>
        </div>

        <footer style="text-align: center; margin-top: 40px; color: #6c757d;">
            <p>🚀 Hãy thử bôi đen bất kỳ đoạn text nào và test extension!</p>
        </footer>
    </div>
</body>
</html>
