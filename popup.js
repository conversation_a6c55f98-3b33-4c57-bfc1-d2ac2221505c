// Popup script để quản lý cấu hình API key

document.addEventListener('DOMContentLoaded', async () => {
  const apiKeyInput = document.getElementById('apiKey');
  const saveBtn = document.getElementById('saveBtn');
  const testBtn = document.getElementById('testBtn');
  const statusDiv = document.getElementById('status');
  
  // Tải API key đã lưu
  const result = await chrome.storage.sync.get(['openaiApiKey']);
  if (result.openaiApiKey) {
    apiKeyInput.value = result.openaiApiKey;
    testBtn.disabled = false;
    showStatus('✅ API key đã được cấu hình', 'success');
  }
  
  // Xử lý khi nhập API key
  apiKeyInput.addEventListener('input', () => {
    const hasKey = apiKeyInput.value.trim().length > 0;
    testBtn.disabled = !hasKey;
    
    if (hasKey && !apiKeyInput.value.startsWith('sk-')) {
      showStatus('⚠️ API key thường bắt đầu bằng "sk-"', 'error');
    } else {
      clearStatus();
    }
  });
  
  // Xử lý lưu API key
  saveBtn.addEventListener('click', async () => {
    const apiKey = apiKeyInput.value.trim();
    
    if (!apiKey) {
      showStatus('❌ Vui lòng nhập API key', 'error');
      return;
    }
    
    if (!apiKey.startsWith('sk-')) {
      showStatus('❌ API key không hợp lệ (phải bắt đầu bằng "sk-")', 'error');
      return;
    }
    
    try {
      saveBtn.disabled = true;
      saveBtn.textContent = '💾 Đang lưu...';
      
      await chrome.storage.sync.set({ openaiApiKey: apiKey });
      testBtn.disabled = false;
      
      showStatus('✅ Đã lưu API key thành công!', 'success');
      
      setTimeout(() => {
        saveBtn.disabled = false;
        saveBtn.textContent = '💾 Lưu cấu hình';
      }, 1000);
      
    } catch (error) {
      showStatus('❌ Lỗi khi lưu API key: ' + error.message, 'error');
      saveBtn.disabled = false;
      saveBtn.textContent = '💾 Lưu cấu hình';
    }
  });
  
  // Xử lý test API
  testBtn.addEventListener('click', async () => {
    const apiKey = apiKeyInput.value.trim();
    
    if (!apiKey) {
      showStatus('❌ Vui lòng nhập API key trước', 'error');
      return;
    }
    
    try {
      testBtn.disabled = true;
      testBtn.textContent = '🧪 Đang test...';
      
      const response = await fetch('https://api.openai.com/v1/models', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      });
      
      if (response.ok) {
        showStatus('✅ API key hoạt động tốt!', 'success');
      } else {
        const errorData = await response.json();
        showStatus('❌ API key không hợp lệ: ' + (errorData.error?.message || 'Unknown error'), 'error');
      }
      
    } catch (error) {
      showStatus('❌ Lỗi khi test API: ' + error.message, 'error');
    } finally {
      testBtn.disabled = false;
      testBtn.textContent = '🧪 Test API';
    }
  });
  
  function showStatus(message, type) {
    statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
  }
  
  function clearStatus() {
    statusDiv.innerHTML = '';
  }
});
