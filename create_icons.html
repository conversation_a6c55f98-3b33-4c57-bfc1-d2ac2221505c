<!DOCTYPE html>
<html>
<head>
    <title>Create Extension Icons</title>
</head>
<body>
    <h2>ChatGPT Translator Icons</h2>
    
    <!-- Icon 16x16 -->
    <div>
        <h3>16x16</h3>
        <canvas id="icon16" width="16" height="16"></canvas>
    </div>
    
    <!-- Icon 48x48 -->
    <div>
        <h3>48x48</h3>
        <canvas id="icon48" width="48" height="48"></canvas>
    </div>
    
    <!-- Icon 128x128 -->
    <div>
        <h3>128x128</h3>
        <canvas id="icon128" width="128" height="128"></canvas>
    </div>
    
    <br>
    <button onclick="downloadIcons()">Download Icons</button>
    
    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // Background
            ctx.fillStyle = '#007bff';
            ctx.fillRect(0, 0, size, size);
            
            // Globe outline
            ctx.strokeStyle = 'white';
            ctx.lineWidth = size * 0.08;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size * 0.35, 0, 2 * Math.PI);
            ctx.stroke();
            
            // Vertical lines (longitude)
            ctx.beginPath();
            ctx.moveTo(size/2, size * 0.15);
            ctx.lineTo(size/2, size * 0.85);
            ctx.stroke();
            
            // Horizontal line (equator)
            ctx.beginPath();
            ctx.moveTo(size * 0.15, size/2);
            ctx.lineTo(size * 0.85, size/2);
            ctx.stroke();
            
            // Translation arrows
            if (size >= 48) {
                ctx.strokeStyle = '#ffd700';
                ctx.lineWidth = size * 0.04;
                
                // Arrow 1
                ctx.beginPath();
                ctx.moveTo(size * 0.25, size * 0.75);
                ctx.lineTo(size * 0.4, size * 0.75);
                ctx.moveTo(size * 0.35, size * 0.7);
                ctx.lineTo(size * 0.4, size * 0.75);
                ctx.lineTo(size * 0.35, size * 0.8);
                ctx.stroke();
                
                // Arrow 2
                ctx.beginPath();
                ctx.moveTo(size * 0.75, size * 0.25);
                ctx.lineTo(size * 0.6, size * 0.25);
                ctx.moveTo(size * 0.65, size * 0.2);
                ctx.lineTo(size * 0.6, size * 0.25);
                ctx.lineTo(size * 0.65, size * 0.3);
                ctx.stroke();
            }
        }
        
        // Draw icons
        drawIcon(document.getElementById('icon16'), 16);
        drawIcon(document.getElementById('icon48'), 48);
        drawIcon(document.getElementById('icon128'), 128);
        
        function downloadIcons() {
            ['icon16', 'icon48', 'icon128'].forEach(id => {
                const canvas = document.getElementById(id);
                const link = document.createElement('a');
                link.download = `${id}.png`;
                link.href = canvas.toDataURL();
                link.click();
            });
        }
    </script>
</body>
</html>
