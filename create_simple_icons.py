#!/usr/bin/env python3
"""
Script đơn giản để tạo icon cho Chrome extension
Y<PERSON><PERSON> cầu: pip install Pillow
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os
except ImportError:
    print("Cần cài đặt Pillow: pip install Pillow")
    exit(1)

def create_icon(size, filename):
    # Tạo image với background xanh
    img = Image.new('RGB', (size, size), '#007bff')
    draw = ImageDraw.Draw(img)
    
    # Vẽ hình tròn (globe)
    center = size // 2
    radius = int(size * 0.35)
    
    # Vẽ viền tròn
    draw.ellipse([center - radius, center - radius, 
                  center + radius, center + radius], 
                 outline='white', width=max(1, size // 20))
    
    # Vẽ đường dọc (kinh tuyến)
    draw.line([center, int(size * 0.15), center, int(size * 0.85)], 
              fill='white', width=max(1, size // 20))
    
    # Vẽ đường ngang (xích đạo)
    draw.line([int(size * 0.15), center, int(size * 0.85), center], 
              fill='white', width=max(1, size // 20))
    
    # Thêm text "T" cho icon nhỏ
    if size <= 16:
        try:
            font = ImageFont.load_default()
            draw.text((center - 4, center - 6), "T", fill='white', font=font)
        except:
            pass
    
    # Lưu file
    img.save(filename)
    print(f"Đã tạo {filename}")

def main():
    # Tạo thư mục icons nếu chưa có
    os.makedirs('icons', exist_ok=True)
    
    # Tạo các icon với kích thước khác nhau
    sizes = [16, 48, 128]
    
    for size in sizes:
        filename = f'icons/icon{size}.png'
        create_icon(size, filename)
    
    print("Hoàn thành tạo icon!")

if __name__ == "__main__":
    main()
